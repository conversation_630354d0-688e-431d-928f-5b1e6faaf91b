<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleIconName</key>
	<string>AppIcon</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>ja</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>14.0</string>
	<key>NSDeviceActivityUsageDescription</key>
	<string>スマートフォンの使用パターンを分析し、デジタルウェルビーイングの向上に役立てます。</string>
	<key>NSFamilyControlsUsageDescription</key>
	<string>アプリ使用時間のデータを取得し、生活習慣の分析に使用します。</string>
	<key>NSPrivacyAccessedAPITypes</key>
	<array>
		<dict>
			<key>NSPrivacyAccessedAPIType</key>
			<string>NSPrivacyAccessedAPICategoryUserDefaults</string>
			<key>NSPrivacyAccessedAPITypeReasons</key>
			<array>
				<string>CA92.1</string>
			</array>
		</dict>
	</array>
	<key>NSUserNotificationUsageDescription</key>
	<string>定期的な心の健康チェックのために通知を使用します</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>定期的に気分や活動を記録するためのリマインダー通知を送信します。</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>購入履歴のスクリーンショットを選択して、購入記録と感情の関連を分析するために使用します。</string>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIApplicationShortcutItems</key>
	<array>
		<dict>
			<key>UIApplicationShortcutItemIconType</key>
			<string>UIApplicationShortcutIconTypeAdd</string>
			<key>UIApplicationShortcutItemSubtitle</key>
			<string>今の気分を素早く記録</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>クイック記録</string>
			<key>UIApplicationShortcutItemType</key>
			<string>com.mentalhealth.quickentry</string>
		</dict>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>background-fetch</string>
		<string>processing</string>
		<string>fetch</string>
	</array>
	<key>UILaunchScreen</key>
	<dict/>
</dict>
</plist>
