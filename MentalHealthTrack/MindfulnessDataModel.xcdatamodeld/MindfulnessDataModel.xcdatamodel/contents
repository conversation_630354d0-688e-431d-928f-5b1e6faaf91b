<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="23788.4" systemVersion="24F74" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithSwiftData="YES" userDefinedModelVersionIdentifier="">
    <entity name="MindfulnessData" representedClassName="MindfulnessData" syncable="YES" codeGenerationType="class">
        <attribute name="activity" optional="YES" attributeType="String"/>
        <attribute name="feelings" optional="YES" attributeType="String"/>
        <attribute name="genre" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="mood" optional="YES" attributeType="String"/>
        <attribute name="tags" optional="YES" attributeType="String"/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    </entity>
</model>