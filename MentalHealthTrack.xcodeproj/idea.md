IDEA
1. マネフォと連携できたら相当いい、彼らはお金の流れに相当強い、しかしも銀行やカードの情報を公開するにはそれなりの関係が必要なのでそこに障壁がある。彼らの情報を取得できるAPIを使えばこのアプリで情報収集できる。しかしその情報をAIに渡していいのか問題がある、これはローカルで動くAIの開発を待つしかない。
2. インスタの視聴履歴や投稿内容などから自分を知る。しかし投稿内容であれば普段の日記から分かりそうなものだ、例えば今日は誰々とどこどこに行ってどう感じたかを見れば投稿内容を追う必要はなくなる、一方で視聴履歴は無意識によるものが強そうなのでとりたいが厳しそう。
3. YouTubeの視聴履歴は多分なんとかなるけど、お金の情報と同じでその情報をOpenAIに渡していいのか？これはユーザーに判断してもらうことも可能で機能を一部限定的にできる。エクスポートした視聴履歴をアプリで投げてもらってそれをOpenAIに投げる。のかカテゴリー分けするだけで見ていたジャンルだけを知るのもあり。
4. スクリーンタイムは有料アカウントにならないと取得できない。


FIXME:一旦無視だけど、横幅が無限に伸びてしまうバグの修正