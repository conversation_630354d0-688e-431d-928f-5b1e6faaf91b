// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		1B3D1E9D2DE93ECD003C4D4F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1B3D1E872DE93ECB003C4D4F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1B3D1E8E2DE93ECB003C4D4F;
			remoteInfo = MentalHealthTrack;
		};
		1B3D1EA72DE93ECD003C4D4F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1B3D1E872DE93ECB003C4D4F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1B3D1E8E2DE93ECB003C4D4F;
			remoteInfo = MentalHealthTrack;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1B3D1E8F2DE93ECB003C4D4F /* MentalHealthTrack.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MentalHealthTrack.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1B3D1E9C2DE93ECD003C4D4F /* MentalHealthTrackTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MentalHealthTrackTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1B3D1EA62DE93ECD003C4D4F /* MentalHealthTrackUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MentalHealthTrackUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		1B13A4B82DEFFB0100E27557 /* Exceptions for "MentalHealthTrack" folder in "MentalHealthTrack" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Resources/Info.plist,
			);
			target = 1B3D1E8E2DE93ECB003C4D4F /* MentalHealthTrack */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1B3D1E912DE93ECB003C4D4F /* MentalHealthTrack */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				1B13A4B82DEFFB0100E27557 /* Exceptions for "MentalHealthTrack" folder in "MentalHealthTrack" target */,
			);
			path = MentalHealthTrack;
			sourceTree = "<group>";
		};
		1B3D1E9F2DE93ECD003C4D4F /* MentalHealthTrackTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MentalHealthTrackTests;
			sourceTree = "<group>";
		};
		1B3D1EA92DE93ECD003C4D4F /* MentalHealthTrackUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MentalHealthTrackUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1B3D1E8C2DE93ECB003C4D4F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B3D1E992DE93ECD003C4D4F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B3D1EA32DE93ECD003C4D4F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1B3D1E862DE93ECB003C4D4F = {
			isa = PBXGroup;
			children = (
				1B3D1E912DE93ECB003C4D4F /* MentalHealthTrack */,
				1B3D1E9F2DE93ECD003C4D4F /* MentalHealthTrackTests */,
				1B3D1EA92DE93ECD003C4D4F /* MentalHealthTrackUITests */,
				1B3D1E902DE93ECB003C4D4F /* Products */,
			);
			sourceTree = "<group>";
		};
		1B3D1E902DE93ECB003C4D4F /* Products */ = {
			isa = PBXGroup;
			children = (
				1B3D1E8F2DE93ECB003C4D4F /* MentalHealthTrack.app */,
				1B3D1E9C2DE93ECD003C4D4F /* MentalHealthTrackTests.xctest */,
				1B3D1EA62DE93ECD003C4D4F /* MentalHealthTrackUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1B3D1E8E2DE93ECB003C4D4F /* MentalHealthTrack */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1B3D1EB02DE93ECD003C4D4F /* Build configuration list for PBXNativeTarget "MentalHealthTrack" */;
			buildPhases = (
				1B3D1E8B2DE93ECB003C4D4F /* Sources */,
				1B3D1E8C2DE93ECB003C4D4F /* Frameworks */,
				1B3D1E8D2DE93ECB003C4D4F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1B3D1E912DE93ECB003C4D4F /* MentalHealthTrack */,
			);
			name = MentalHealthTrack;
			packageProductDependencies = (
			);
			productName = MentalHealthTrack;
			productReference = 1B3D1E8F2DE93ECB003C4D4F /* MentalHealthTrack.app */;
			productType = "com.apple.product-type.application";
		};
		1B3D1E9B2DE93ECD003C4D4F /* MentalHealthTrackTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1B3D1EB32DE93ECD003C4D4F /* Build configuration list for PBXNativeTarget "MentalHealthTrackTests" */;
			buildPhases = (
				1B3D1E982DE93ECD003C4D4F /* Sources */,
				1B3D1E992DE93ECD003C4D4F /* Frameworks */,
				1B3D1E9A2DE93ECD003C4D4F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1B3D1E9E2DE93ECD003C4D4F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1B3D1E9F2DE93ECD003C4D4F /* MentalHealthTrackTests */,
			);
			name = MentalHealthTrackTests;
			packageProductDependencies = (
			);
			productName = MentalHealthTrackTests;
			productReference = 1B3D1E9C2DE93ECD003C4D4F /* MentalHealthTrackTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1B3D1EA52DE93ECD003C4D4F /* MentalHealthTrackUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1B3D1EB62DE93ECD003C4D4F /* Build configuration list for PBXNativeTarget "MentalHealthTrackUITests" */;
			buildPhases = (
				1B3D1EA22DE93ECD003C4D4F /* Sources */,
				1B3D1EA32DE93ECD003C4D4F /* Frameworks */,
				1B3D1EA42DE93ECD003C4D4F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1B3D1EA82DE93ECD003C4D4F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1B3D1EA92DE93ECD003C4D4F /* MentalHealthTrackUITests */,
			);
			name = MentalHealthTrackUITests;
			packageProductDependencies = (
			);
			productName = MentalHealthTrackUITests;
			productReference = 1B3D1EA62DE93ECD003C4D4F /* MentalHealthTrackUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1B3D1E872DE93ECB003C4D4F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					1B3D1E8E2DE93ECB003C4D4F = {
						CreatedOnToolsVersion = 16.3;
					};
					1B3D1E9B2DE93ECD003C4D4F = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 1B3D1E8E2DE93ECB003C4D4F;
					};
					1B3D1EA52DE93ECD003C4D4F = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 1B3D1E8E2DE93ECB003C4D4F;
					};
				};
			};
			buildConfigurationList = 1B3D1E8A2DE93ECB003C4D4F /* Build configuration list for PBXProject "MentalHealthTrack" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1B3D1E862DE93ECB003C4D4F;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 1B3D1E902DE93ECB003C4D4F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1B3D1E8E2DE93ECB003C4D4F /* MentalHealthTrack */,
				1B3D1E9B2DE93ECD003C4D4F /* MentalHealthTrackTests */,
				1B3D1EA52DE93ECD003C4D4F /* MentalHealthTrackUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1B3D1E8D2DE93ECB003C4D4F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B3D1E9A2DE93ECD003C4D4F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B3D1EA42DE93ECD003C4D4F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1B3D1E8B2DE93ECB003C4D4F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B3D1E982DE93ECD003C4D4F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1B3D1EA22DE93ECD003C4D4F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1B3D1E9E2DE93ECD003C4D4F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1B3D1E8E2DE93ECB003C4D4F /* MentalHealthTrack */;
			targetProxy = 1B3D1E9D2DE93ECD003C4D4F /* PBXContainerItemProxy */;
		};
		1B3D1EA82DE93ECD003C4D4F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1B3D1E8E2DE93ECB003C4D4F /* MentalHealthTrack */;
			targetProxy = 1B3D1EA72DE93ECD003C4D4F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1B3D1EAE2DE93ECD003C4D4F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1B3D1EAF2DE93ECD003C4D4F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1B3D1EB12DE93ECD003C4D4F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MentalHealthTrack/MentalHealthTrack.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7AF4643Y3L;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MentalHealthTrack/Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Compass for the Mind";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "歩数、心拍数、睡眠データを読み取り、あなたの健康状態と気分の関連性を分析します。";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "健康データを更新して、より正確な分析を提供します。";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Kaz.MentalHealthTrack;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1B3D1EB22DE93ECD003C4D4F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MentalHealthTrack/MentalHealthTrack.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7AF4643Y3L;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MentalHealthTrack/Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Compass for the Mind";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "歩数、心拍数、睡眠データを読み取り、あなたの健康状態と気分の関連性を分析します。";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "健康データを更新して、より正確な分析を提供します。";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Kaz.MentalHealthTrack;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1B3D1EB42DE93ECD003C4D4F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Kaz.MentalHealthTrackTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MentalHealthTrack.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MentalHealthTrack";
			};
			name = Debug;
		};
		1B3D1EB52DE93ECD003C4D4F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Kaz.MentalHealthTrackTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MentalHealthTrack.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MentalHealthTrack";
			};
			name = Release;
		};
		1B3D1EB72DE93ECD003C4D4F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Kaz.MentalHealthTrackUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MentalHealthTrack;
			};
			name = Debug;
		};
		1B3D1EB82DE93ECD003C4D4F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Kaz.MentalHealthTrackUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MentalHealthTrack;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1B3D1E8A2DE93ECB003C4D4F /* Build configuration list for PBXProject "MentalHealthTrack" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B3D1EAE2DE93ECD003C4D4F /* Debug */,
				1B3D1EAF2DE93ECD003C4D4F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1B3D1EB02DE93ECD003C4D4F /* Build configuration list for PBXNativeTarget "MentalHealthTrack" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B3D1EB12DE93ECD003C4D4F /* Debug */,
				1B3D1EB22DE93ECD003C4D4F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1B3D1EB32DE93ECD003C4D4F /* Build configuration list for PBXNativeTarget "MentalHealthTrackTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B3D1EB42DE93ECD003C4D4F /* Debug */,
				1B3D1EB52DE93ECD003C4D4F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1B3D1EB62DE93ECD003C4D4F /* Build configuration list for PBXNativeTarget "MentalHealthTrackUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1B3D1EB72DE93ECD003C4D4F /* Debug */,
				1B3D1EB82DE93ECD003C4D4F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1B3D1E872DE93ECB003C4D4F /* Project object */;
}
